using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Forms.Dialogs
{
    /// <summary>
    /// Dialog for creating and editing roles with validation
    /// </summary>
    public partial class RoleCreateEditDialog : XtraForm
    {
        #region Private Fields

        private int _roleId = 0;
        private bool _isEditMode = false;
        private RoleModel _originalRole;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the role ID (0 for new role)
        /// </summary>
        public int RoleId => _roleId;

        /// <summary>
        /// Gets whether this is edit mode
        /// </summary>
        public bool IsEditMode => _isEditMode;

        /// <summary>
        /// Gets the role name entered by user
        /// </summary>
        public string RoleName => txtRoleName.Text.Trim();

        /// <summary>
        /// Gets the role description entered by user
        /// </summary>
        public string RoleDescription => txtDescription.Text.Trim();

        /// <summary>
        /// Gets whether the role is active
        /// </summary>
        public bool IsActive => chkIsActive.Checked;

        #endregion

        #region Constructors

        /// <summary>
        /// Constructor for creating new role
        /// </summary>
        public RoleCreateEditDialog()
        {
            InitializeComponent();
            InitializeForNewRole();
        }

        /// <summary>
        /// Constructor for editing existing role
        /// </summary>
        /// <param name="roleId">ID of role to edit</param>
        public RoleCreateEditDialog(int roleId)
        {
            InitializeComponent();
            InitializeForEditRole(roleId);
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initialize dialog for creating new role
        /// </summary>
        private void InitializeForNewRole()
        {
            try
            {
                _isEditMode = false;
                _roleId = 0;

                this.Text = "Create New Role";
                lblTitle.Text = "Create New Role";

                // Set default values
                txtRoleName.Text = "";
                txtDescription.Text = "";
                chkIsActive.Checked = true;

                // Focus on role name
                txtRoleName.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing dialog for new role: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Initialize dialog for editing existing role
        /// </summary>
        /// <param name="roleId">ID of role to edit</param>
        private void InitializeForEditRole(int roleId)
        {
            try
            {
                _isEditMode = true;
                _roleId = roleId;

                this.Text = "Edit Role";
                lblTitle.Text = "Edit Role";

                // Load role data
                LoadRoleData();

                // Focus on role name
                txtRoleName.Focus();
                txtRoleName.SelectAll();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing dialog for edit role: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Load role data for editing
        /// </summary>
        private void LoadRoleData()
        {
            try
            {
                var role = PermissionService.GetRoleById(_roleId);
                if (role != null)
                {
                    _originalRole = role;
                    txtRoleName.Text = role.RoleName;
                    txtDescription.Text = role.Description ?? "";
                    chkIsActive.Checked = role.IsActive;
                }
                else
                {
                    MessageBox.Show("Role not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.DialogResult = DialogResult.Cancel;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading role data: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle OK button click
        /// </summary>
        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateInput())
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error processing OK click: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Cancel button click
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// Handle role name text change for validation
        /// </summary>
        private void txtRoleName_TextChanged(object sender, EventArgs e)
        {
            ValidateRoleName();
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validate all input fields
        /// </summary>
        /// <returns>True if all input is valid</returns>
        private bool ValidateInput()
        {
            try
            {
                // Clear any previous error messages
                lblError.Text = "";
                lblError.Visible = false;

                // Validate role name
                if (!ValidateRoleName())
                    return false;

                // Check for duplicate role name (only if creating new or name changed)
                if (!_isEditMode || (_originalRole != null && _originalRole.RoleName != txtRoleName.Text.Trim()))
                {
                    if (PermissionService.RoleNameExists(txtRoleName.Text.Trim()))
                    {
                        ShowError("A role with this name already exists. Please choose a different name.");
                        txtRoleName.Focus();
                        txtRoleName.SelectAll();
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                ShowError($"Error validating input: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Validate role name field
        /// </summary>
        /// <returns>True if role name is valid</returns>
        private bool ValidateRoleName()
        {
            string roleName = txtRoleName.Text.Trim();

            if (string.IsNullOrEmpty(roleName))
            {
                ShowError("Role name is required.");
                txtRoleName.Focus();
                return false;
            }

            if (roleName.Length < 2)
            {
                ShowError("Role name must be at least 2 characters long.");
                txtRoleName.Focus();
                return false;
            }

            if (roleName.Length > 50)
            {
                ShowError("Role name cannot exceed 50 characters.");
                txtRoleName.Focus();
                return false;
            }

            // Check for invalid characters
            if (roleName.Contains("|") || roleName.Contains(";") || roleName.Contains(","))
            {
                ShowError("Role name cannot contain special characters: | ; ,");
                txtRoleName.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// Show error message to user
        /// </summary>
        /// <param name="message">Error message to display</param>
        private void ShowError(string message)
        {
            lblError.Text = message;
            lblError.Visible = true;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Get role data for saving
        /// </summary>
        /// <returns>Role model with current data</returns>
        public RoleModel GetRoleData()
        {
            return new RoleModel
            {
                RoleId = _roleId,
                RoleName = txtRoleName.Text.Trim(),
                Description = txtDescription.Text.Trim(),
                IsActive = chkIsActive.Checked,
                CreatedDate = _originalRole?.CreatedDate ?? DateTime.Now,
                ModifiedDate = DateTime.Now
            };
        }

        #endregion
    }
}
