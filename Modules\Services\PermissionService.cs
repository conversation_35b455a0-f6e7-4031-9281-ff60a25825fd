using System;
using System.Collections.Generic;
using System.Linq;
using ProManage.Modules.Connections;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Main permission service that provides the core logic for checking user permissions.
    /// Implements the 2-level permission system (role permissions + user overrides) and serves
    /// as the primary interface for all permission checks throughout the application.
    /// </summary>
    public static class PermissionService
    {
        private static readonly PermissionCache _cache = new PermissionCache();

        #region Core Permission Checking

        /// <summary>
        /// Check if user has specific permission for a form
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <param name="permissionType">Permission type (read, new, edit, delete, print)</param>
        /// <returns>True if user has permission</returns>
        public static bool HasPermission(int userId, string formName, PermissionType permissionType)
        {
            try
            {
                // Try user permission set first (fastest)
                if (_cache.TryGetUserPermission(userId, formName, permissionType, out bool userSetResult))
                {
                    return userSetResult;
                }

                // Fall back to individual cache check
                var cacheKey = $"{userId}_{formName}_{permissionType}";
                if (_cache.TryGetPermission(cacheKey, out bool cachedResult))
                {
                    return cachedResult;
                }

                // Database lookup and cache
                var result = CheckPermissionFromDatabase(userId, formName, permissionType);
                var source = GetPermissionSource(userId, formName);
                _cache.SetPermission(cacheKey, result, source);
                return result;
            }
            catch (Exception ex)
            {
                // Log error and deny permission for security
                System.Diagnostics.Debug.WriteLine($"Permission check error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Check permission directly from database (for caching)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <param name="permissionType">Permission type</param>
        /// <returns>True if permission is granted</returns>
        private static bool CheckPermissionFromDatabase(int userId, string formName, PermissionType permissionType)
        {
            // 1. Check user override (NULL = inherit from role)
            var userPermission = GetUserPermissionValue(userId, formName, permissionType);
            if (userPermission.HasValue)
            {
                return userPermission.Value;
            }

            // 2. Check role permission
            return GetRolePermissionValue(userId, formName, permissionType);
        }

        /// <summary>
        /// Check if user has global permission (user management)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="permissionType">Global permission type</param>
        /// <returns>True if user has global permission</returns>
        public static bool HasGlobalPermission(int userId, GlobalPermissionType permissionType)
        {
            try
            {
                // Check cache first
                var cacheKey = $"global_{userId}_{permissionType}";
                if (_cache.TryGetPermission(cacheKey, out bool cachedResult))
                {
                    return cachedResult;
                }

                var globalPermissions = PermissionDatabaseService.GetGlobalPermissions(userId);
                if (globalPermissions == null)
                {
                    _cache.SetPermission(cacheKey, false);
                    return false;
                }

                bool result = permissionType switch
                {
                    GlobalPermissionType.CanCreateUsers => globalPermissions.CanCreateUsers,
                    GlobalPermissionType.CanEditUsers => globalPermissions.CanEditUsers,
                    GlobalPermissionType.CanDeleteUsers => globalPermissions.CanDeleteUsers,
                    GlobalPermissionType.CanPrintUsers => globalPermissions.CanPrintUsers,
                    _ => false
                };

                _cache.SetPermission(cacheKey, result);
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Global permission check error: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Data Retrieval Methods

        /// <summary>
        /// Get all active roles
        /// </summary>
        /// <returns>List of active roles</returns>
        public static List<Role> GetAllRoles()
        {
            try
            {
                return PermissionDatabaseService.GetAllRoles();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting all roles: {ex.Message}");
                return new List<Role>();
            }
        }

        /// <summary>
        /// Get all active users
        /// </summary>
        /// <returns>List of active users</returns>
        public static List<UserInfo> GetAllUsers()
        {
            try
            {
                return PermissionDatabaseService.GetAllUsers();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting all users: {ex.Message}");
                return new List<UserInfo>();
            }
        }

        /// <summary>
        /// Get role permissions for a specific role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>List of role permissions</returns>
        public static List<RolePermission> GetRolePermissions(int roleId)
        {
            try
            {
                return PermissionDatabaseService.GetRolePermissions(roleId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting role permissions: {ex.Message}");
                return new List<RolePermission>();
            }
        }

        /// <summary>
        /// Get global permissions for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Global permissions or null if not found</returns>
        public static GlobalPermission GetGlobalPermissions(int userId)
        {
            try
            {
                return PermissionDatabaseService.GetGlobalPermissions(userId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting global permissions: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region Permission Resolution

        /// <summary>
        /// Get user permission value (returns null if inherit from role)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <param name="permissionType">Permission type</param>
        /// <returns>User permission value or null if inherit from role</returns>
        private static bool? GetUserPermissionValue(int userId, string formName, PermissionType permissionType)
        {
            var userPermission = PermissionDatabaseService.GetUserPermission(userId, formName);
            if (userPermission == null) return null;

            return permissionType switch
            {
                PermissionType.Read => userPermission.ReadPermission,
                PermissionType.New => userPermission.NewPermission,
                PermissionType.Edit => userPermission.EditPermission,
                PermissionType.Delete => userPermission.DeletePermission,
                PermissionType.Print => userPermission.PrintPermission,
                _ => null
            };
        }

        /// <summary>
        /// Get role permission value
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <param name="permissionType">Permission type</param>
        /// <returns>Role permission value</returns>
        private static bool GetRolePermissionValue(int userId, string formName, PermissionType permissionType)
        {
            // First get user's role
            var user = GetUserWithRole(userId);
            if (user == null) return false;

            var rolePermission = PermissionDatabaseService.GetRolePermission(user.RoleId, formName);
            if (rolePermission == null) return false;

            return permissionType switch
            {
                PermissionType.Read => rolePermission.ReadPermission,
                PermissionType.New => rolePermission.NewPermission,
                PermissionType.Edit => rolePermission.EditPermission,
                PermissionType.Delete => rolePermission.DeletePermission,
                PermissionType.Print => rolePermission.PrintPermission,
                _ => false
            };
        }

        /// <summary>
        /// Get user with role information from database
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>User with role information</returns>
        private static UserWithRole GetUserWithRole(int userId)
        {
            return PermissionDatabaseService.GetUserWithRole(userId);
        }

        #endregion

        #region Bulk Permission Operations

        /// <summary>
        /// Get all effective permissions for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of effective permissions for all forms</returns>
        public static List<EffectivePermission> GetUserEffectivePermissions(int userId)
        {
            try
            {
                var effectivePermissions = new List<EffectivePermission>();
                var allForms = FormsConfigurationService.GetAllForms();

                foreach (var form in allForms)
                {
                    var permission = new EffectivePermission
                    {
                        FormName = form.FormName,
                        ReadPermission = HasPermission(userId, form.FormName, PermissionType.Read),
                        NewPermission = HasPermission(userId, form.FormName, PermissionType.New),
                        EditPermission = HasPermission(userId, form.FormName, PermissionType.Edit),
                        DeletePermission = HasPermission(userId, form.FormName, PermissionType.Delete),
                        PrintPermission = HasPermission(userId, form.FormName, PermissionType.Print),
                        Source = GetPermissionSource(userId, form.FormName)
                    };

                    effectivePermissions.Add(permission);
                }

                return effectivePermissions;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting effective permissions: {ex.Message}");
                return new List<EffectivePermission>();
            }
        }

        /// <summary>
        /// Get list of forms user can access (has read permission)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of form names user can access</returns>
        public static List<string> GetVisibleForms(int userId)
        {
            try
            {
                var visibleForms = new List<string>();
                var allForms = FormsConfigurationService.GetAllForms();

                foreach (var form in allForms)
                {
                    if (HasPermission(userId, form.FormName, PermissionType.Read))
                    {
                        visibleForms.Add(form.FormName);
                    }
                }

                return visibleForms;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting visible forms: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// Determine if permission comes from role or user override
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <returns>Permission source</returns>
        private static PermissionSource GetPermissionSource(int userId, string formName)
        {
            var userPermission = PermissionDatabaseService.GetUserPermission(userId, formName);
            return userPermission != null ? PermissionSource.UserOverride : PermissionSource.Role;
        }

        #endregion

        #region Permission Updates

        /// <summary>
        /// Update role permissions
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="updates">List of permission updates</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool UpdateRolePermissions(int roleId, List<RolePermissionUpdate> updates)
        {
            try
            {
                var success = PermissionDatabaseService.UpdateRolePermissions(updates);
                if (success)
                {
                    // Clear cache for affected users
                    _cache.ClearRolePermissions(roleId);
                }
                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating role permissions: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Update user permission overrides
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="updates">List of permission updates</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool UpdateUserPermissions(int userId, List<UserPermissionUpdate> updates)
        {
            try
            {
                var success = PermissionDatabaseService.UpdateUserPermissions(updates);
                if (success)
                {
                    // Clear cache for user
                    _cache.ClearUserPermissions(userId);
                }
                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating user permissions: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Update global permissions
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="update">Global permission update</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool UpdateGlobalPermissions(int userId, GlobalPermissionUpdate update)
        {
            try
            {
                var success = PermissionDatabaseService.UpdateGlobalPermissions(update);
                if (success)
                {
                    // Clear global permission cache for user
                    _cache.ClearGlobalPermissions(userId);
                }
                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating global permissions: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reset user permissions to role defaults (remove all user overrides)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool ResetUserPermissions(int userId)
        {
            try
            {
                var success = PermissionDatabaseService.ResetUserPermissions(userId);
                if (success)
                {
                    // Clear cache for user
                    _cache.ClearUserPermissions(userId);
                }
                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error resetting user permissions: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Clear all permission cache
        /// </summary>
        public static void ClearCache()
        {
            _cache.ClearAll();
        }

        #endregion

        #region Cache Management

        /// <summary>
        /// Clear all permission cache
        /// </summary>
        public static void ClearPermissionCache()
        {
            _cache.ClearAll();
        }

        /// <summary>
        /// Clear permission cache for specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        public static void ClearUserPermissionCache(int userId)
        {
            _cache.ClearUserPermissions(userId);
        }

        /// <summary>
        /// Get cache statistics
        /// </summary>
        /// <returns>Cache statistics information</returns>
        public static CacheStatistics GetCacheStatistics()
        {
            return _cache.GetStatistics();
        }

        /// <summary>
        /// Clean expired cache entries
        /// </summary>
        public static void CleanExpiredCache()
        {
            _cache.CleanExpiredEntries();
        }

        /// <summary>
        /// Warm cache for frequently accessed users and forms
        /// </summary>
        /// <param name="userIds">List of user IDs to warm (optional)</param>
        /// <param name="formNames">List of form names to warm (optional)</param>
        public static async System.Threading.Tasks.Task WarmPermissionCache(List<int> userIds = null, List<string> formNames = null)
        {
            try
            {
                var preloader = new PermissionPreloader();

                // Use provided lists or get defaults
                var usersToWarm = userIds ?? preloader.GetActiveUserIds(50);
                var formsToWarm = formNames ?? preloader.GetFrequentlyAccessedForms(20);

                await _cache.WarmCache(usersToWarm, formsToWarm);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error warming permission cache: {ex.Message}");
            }
        }

        /// <summary>
        /// Preload permissions for a specific user
        /// </summary>
        /// <param name="userId">User ID to preload</param>
        /// <param name="formNames">List of form names (optional)</param>
        public static void PreloadUserPermissions(int userId, List<string> formNames = null)
        {
            try
            {
                var preloader = new PermissionPreloader();
                var formsToLoad = formNames ?? preloader.GetAllFormNames();
                var permissions = preloader.LoadUserPermissions(userId, formsToLoad);
                _cache.PreloadUserPermissions(userId, permissions);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error preloading user permissions: {ex.Message}");
            }
        }

        /// <summary>
        /// Optimize cache by removing least recently used entries
        /// </summary>
        /// <param name="maxEntries">Maximum number of entries to keep</param>
        public static void OptimizePermissionCache(int maxEntries = 1000)
        {
            _cache.OptimizeCache(maxEntries);
        }

        /// <summary>
        /// Get most accessed permissions for analysis
        /// </summary>
        /// <param name="topCount">Number of top permissions to return</param>
        /// <returns>List of most accessed permission keys</returns>
        public static List<string> GetMostAccessedPermissions(int topCount = 10)
        {
            return _cache.GetMostAccessedPermissions(topCount);
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validate permission request
        /// </summary>
        /// <param name="request">Permission request to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool ValidatePermissionRequest(PermissionRequest request)
        {
            if (request == null) return false;
            if (request.UserId <= 0) return false;
            if (string.IsNullOrEmpty(request.FormName)) return false;

            // Check if form exists in configuration
            return FormsConfigurationService.FormExists(request.FormName);
        }

        #endregion

        #region Role Management Methods

        /// <summary>
        /// Get role by ID
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>Role model or null if not found</returns>
        public static RoleModel GetRoleById(int roleId)
        {
            try
            {
                return PermissionDatabaseService.GetRoleById(roleId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting role by ID: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Check if role name exists (for validation)
        /// </summary>
        /// <param name="roleName">Role name to check</param>
        /// <param name="excludeRoleId">Role ID to exclude from check (for edit scenarios)</param>
        /// <returns>True if role name exists</returns>
        public static bool RoleNameExists(string roleName, int excludeRoleId = 0)
        {
            try
            {
                return PermissionDatabaseService.RoleNameExists(roleName, excludeRoleId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking role name existence: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Create new role
        /// </summary>
        /// <param name="role">Role data</param>
        /// <returns>New role ID if successful, 0 if failed</returns>
        public static int CreateRole(RoleModel role)
        {
            try
            {
                var newRoleId = PermissionDatabaseService.CreateRole(role);
                if (newRoleId > 0)
                {
                    // Clear cache to ensure fresh data
                    _cache.ClearAll();
                }
                return newRoleId;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating role: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Update existing role
        /// </summary>
        /// <param name="role">Role data</param>
        /// <returns>True if successful</returns>
        public static bool UpdateRole(RoleModel role)
        {
            try
            {
                var success = PermissionDatabaseService.UpdateRole(role);
                if (success)
                {
                    // Clear cache for affected role
                    _cache.ClearRolePermissions(role.RoleId);
                }
                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating role: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Delete role (only if not assigned to any users)
        /// </summary>
        /// <param name="roleId">Role ID to delete</param>
        /// <returns>True if successful</returns>
        public static bool DeleteRole(int roleId)
        {
            try
            {
                var success = PermissionDatabaseService.DeleteRole(roleId);
                if (success)
                {
                    // Clear cache for deleted role
                    _cache.ClearRolePermissions(roleId);
                }
                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting role: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Copy permissions from one role to another
        /// </summary>
        /// <param name="sourceRoleId">Source role ID</param>
        /// <param name="targetRoleId">Target role ID</param>
        /// <returns>True if successful</returns>
        public static bool CopyRolePermissions(int sourceRoleId, int targetRoleId)
        {
            try
            {
                var success = PermissionDatabaseService.CopyRolePermissions(sourceRoleId, targetRoleId);
                if (success)
                {
                    // Clear cache for target role
                    _cache.ClearRolePermissions(targetRoleId);
                }
                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error copying role permissions: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get user effective permissions for a specific form (for MenuRibbon UC)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <returns>Form permission set</returns>
        public static FormPermissionSet GetUserEffectivePermissions(int userId, string formName)
        {
            try
            {
                return new FormPermissionSet
                {
                    FormName = formName,
                    CanRead = HasPermission(userId, formName, PermissionType.Read),
                    CanCreate = HasPermission(userId, formName, PermissionType.New),
                    CanEdit = HasPermission(userId, formName, PermissionType.Edit),
                    CanDelete = HasPermission(userId, formName, PermissionType.Delete),
                    CanPrint = HasPermission(userId, formName, PermissionType.Print)
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting user effective permissions: {ex.Message}");
                return new FormPermissionSet
                {
                    FormName = formName,
                    CanRead = false,
                    CanCreate = false,
                    CanEdit = false,
                    CanDelete = false,
                    CanPrint = false
                };
            }
        }

        #endregion
    }
}
